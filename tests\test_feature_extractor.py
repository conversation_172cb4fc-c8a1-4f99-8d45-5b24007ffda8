# Tests for feature extraction functionality
# From Has<PERSON>'s Workspace

import unittest
import sys
import os

# Add backend to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from backend.feature_extractor import (
    extract_features_from_code,
    features_to_vector,
    get_code_complexity_metrics,
    analyze_code_structure,
    MODEL_EXPECTED_FEATURES_ORDER,
)


class TestFeatureExtractor(unittest.TestCase):
    """Test cases for feature extraction functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.simple_code = '''
def hello_world():
    """Simple function."""
    print("Hello, World!")
    return True

if __name__ == "__main__":
    hello_world()
'''

        self.complex_code = '''
import os
import sys
from typing import List, Dict

class DataProcessor:
    """Process data with various methods."""
    
    def __init__(self, data: List[Dict]):
        self.data = data
        self.processed = []
    
    def process(self):
        """Process all data items."""
        for item in self.data:
            try:
                if isinstance(item, dict):
                    result = self._process_dict(item)
                    self.processed.append(result)
                else:
                    self.processed.append(None)
            except Exception as e:
                print(f"Error processing item: {e}")
                continue
        
        return self.processed
    
    def _process_dict(self, item: Dict) -> Dict:
        """Process a dictionary item."""
        result = {}
        for key, value in item.items():
            if isinstance(value, (int, float)):
                result[key] = value * 2
            elif isinstance(value, str):
                result[key] = value.upper()
            else:
                result[key] = str(value)
        return result

def main():
    """Main function."""
    data = [
        {"name": "test", "value": 10},
        {"name": "example", "value": 20}
    ]
    
    processor = DataProcessor(data)
    results = processor.process()
    
    for result in results:
        if result:
            print(f"Processed: {result}")

if __name__ == "__main__":
    main()
'''

    def test_extract_features_simple_code(self):
        """Test feature extraction from simple code."""
        features = extract_features_from_code(self.simple_code)

        # Check that features were extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)

        # Check specific features
        self.assertEqual(features.get("FunctionDef_count", 0), 1)
        self.assertEqual(features.get("If_count", 0), 1)
        self.assertGreater(features.get("total_nodes", 0), 0)
        self.assertGreater(features.get("unique_node_types", 0), 0)

    def test_extract_features_complex_code(self):
        """Test feature extraction from complex code."""
        features = extract_features_from_code(self.complex_code)

        # Check that features were extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)

        # Check specific features for complex code
        self.assertGreater(features.get("FunctionDef_count", 0), 1)
        self.assertEqual(features.get("ClassDef_count", 0), 1)
        self.assertGreater(features.get("For_count", 0), 0)
        self.assertGreater(features.get("Try_count", 0), 0)
        self.assertGreater(features.get("Import_count", 0), 0)

    def test_extract_features_empty_code(self):
        """Test feature extraction from empty code."""
        features = extract_features_from_code("")
        self.assertEqual(features, {})

        features = extract_features_from_code("   \n  \t  ")
        self.assertEqual(features, {})

    def test_extract_features_invalid_syntax(self):
        """Test feature extraction from code with syntax errors."""
        invalid_code = """
def broken_function(
    print("This has syntax errors"
    return
"""
        features = extract_features_from_code(invalid_code)
        self.assertEqual(features, {})

    def test_features_to_vector(self):
        """Test conversion of features to vector format."""
        features = extract_features_from_code(self.simple_code)
        vector = features_to_vector(features)

        # Check vector properties
        self.assertIsInstance(vector, list)
        self.assertEqual(len(vector), len(MODEL_EXPECTED_FEATURES_ORDER))

        # Check that all values are integers
        for value in vector:
            self.assertIsInstance(value, int)

    def test_get_code_complexity_metrics(self):
        """Test complexity metrics calculation."""
        features = extract_features_from_code(self.complex_code)
        metrics = get_code_complexity_metrics(features)

        # Check metrics structure
        expected_keys = [
            "cyclomatic_complexity",
            "function_density",
            "class_density",
            "control_flow_density",
            "total_functions",
            "total_classes",
            "total_control_structures",
        ]

        for key in expected_keys:
            self.assertIn(key, metrics)
            self.assertIsInstance(metrics[key], (int, float))

        # Check reasonable values
        self.assertGreater(metrics["cyclomatic_complexity"], 0)
        self.assertGreaterEqual(metrics["function_density"], 0)
        self.assertGreaterEqual(metrics["class_density"], 0)

    def test_analyze_code_structure(self):
        """Test code structure analysis."""
        features = extract_features_from_code(self.complex_code)
        analysis = analyze_code_structure(features)

        # Check analysis structure
        self.assertIn("structural_insights", analysis)
        self.assertIn("complexity_level", analysis)

        self.assertIsInstance(analysis["structural_insights"], list)
        self.assertIsInstance(analysis["complexity_level"], str)

        # Check complexity level is valid
        valid_levels = ["Low", "Medium", "High"]
        self.assertIn(analysis["complexity_level"], valid_levels)

    def test_model_expected_features_order(self):
        """Test that MODEL_EXPECTED_FEATURES_ORDER is properly defined."""
        # Check that it's a list
        self.assertIsInstance(MODEL_EXPECTED_FEATURES_ORDER, list)

        # Check that it has reasonable length
        self.assertGreater(len(MODEL_EXPECTED_FEATURES_ORDER), 10)

        # Check that all items are strings
        for feature in MODEL_EXPECTED_FEATURES_ORDER:
            self.assertIsInstance(feature, str)

        # Check for expected features
        expected_features = [
            "FunctionDef_count",
            "ClassDef_count",
            "If_count",
            "For_count",
            "total_nodes",
            "unique_node_types",
        ]

        for feature in expected_features:
            self.assertIn(feature, MODEL_EXPECTED_FEATURES_ORDER)

    def test_feature_consistency(self):
        """Test that feature extraction is consistent."""
        # Extract features multiple times
        features1 = extract_features_from_code(self.simple_code)
        features2 = extract_features_from_code(self.simple_code)

        # Should be identical
        self.assertEqual(features1, features2)

    def test_all_expected_features_present(self):
        """Test that all expected features are present in extraction."""
        features = extract_features_from_code(self.complex_code)

        # Check that all expected features are present
        for expected_feature in MODEL_EXPECTED_FEATURES_ORDER:
            self.assertIn(expected_feature, features)


if __name__ == "__main__":
    unittest.main()
