# run_backend.py
# Simple script to run the backend server
# From <PERSON><PERSON>'s Workspace

"""
Simple script to run the FastAPI backend server for development.
"""

import uvicorn
import os
import sys
import logging

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def main():
    """Run the backend server."""
    print("🚀 Starting AI-Enhanced Code Review Tool Backend")
    print("=" * 50)
    print("Backend API will be available at: http://localhost:8000")
    print("API Documentation: http://localhost:8000/docs")
    print("Health Check: http://localhost:8000/health")
    print("=" * 50)

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    try:
        # Run the server
        uvicorn.run(
            "backend.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Backend server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
