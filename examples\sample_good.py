# sample_good.py
# Example of well-written Python code
# From <PERSON><PERSON>'s Workspace

"""
This module demonstrates good Python coding practices.
It includes proper documentation, error handling, and clean structure.
"""

import logging
from typing import List, Dict, Optional, Union


def calculate_statistics(numbers: List[Union[int, float]]) -> Dict[str, float]:
    """
    Calculate basic statistics for a list of numbers.

    Args:
        numbers: List of numeric values to analyze.

    Returns:
        Dictionary containing mean, median, and standard deviation.

    Raises:
        ValueError: If the input list is empty.
        TypeError: If the input contains non-numeric values.
    """
    if not numbers:
        raise ValueError("Cannot calculate statistics for empty list")

    # Validate input types
    for num in numbers:
        if not isinstance(num, (int, float)):
            raise TypeError(f"Expected numeric value, got {type(num).__name__}")

    # Calculate statistics
    mean = sum(numbers) / len(numbers)
    sorted_numbers = sorted(numbers)

    # Calculate median
    n = len(sorted_numbers)
    if n % 2 == 0:
        median = (sorted_numbers[n // 2 - 1] + sorted_numbers[n // 2]) / 2
    else:
        median = sorted_numbers[n // 2]

    # Calculate standard deviation
    variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)
    std_dev = variance**0.5

    return {
        "mean": round(mean, 2),
        "median": round(median, 2),
        "std_dev": round(std_dev, 2),
        "count": len(numbers),
    }


class DataProcessor:
    """
    A class for processing and analyzing numerical data.

    This class provides methods for data validation, transformation,
    and statistical analysis.
    """

    def __init__(self, data: Optional[List[Union[int, float]]] = None):
        """
        Initialize the DataProcessor.

        Args:
            data: Optional initial data to process.
        """
        self.data = data or []
        self.logger = logging.getLogger(__name__)

    def add_data(self, values: List[Union[int, float]]) -> None:
        """
        Add new data points to the processor.

        Args:
            values: List of numeric values to add.

        Raises:
            TypeError: If values contain non-numeric data.
        """
        for value in values:
            if not isinstance(value, (int, float)):
                raise TypeError(f"Expected numeric value, got {type(value).__name__}")

        self.data.extend(values)
        self.logger.info(f"Added {len(values)} data points")

    def get_statistics(self) -> Dict[str, float]:
        """
        Get statistics for the current data.

        Returns:
            Dictionary containing statistical measures.
        """
        if not self.data:
            return {"count": 0}

        return calculate_statistics(self.data)

    def filter_outliers(self, threshold: float = 2.0) -> List[Union[int, float]]:
        """
        Filter out outliers using standard deviation threshold.

        Args:
            threshold: Number of standard deviations for outlier detection.

        Returns:
            List of values without outliers.
        """
        if len(self.data) < 2:
            return self.data.copy()

        stats = self.get_statistics()
        mean = stats["mean"]
        std_dev = stats["std_dev"]

        filtered_data = [
            value for value in self.data if abs(value - mean) <= threshold * std_dev
        ]

        self.logger.info(f"Filtered {len(self.data) - len(filtered_data)} outliers")
        return filtered_data


def main() -> None:
    """
    Demonstrate the usage of the DataProcessor class.
    """
    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Sample data
    sample_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 100]  # 100 is an outlier

    try:
        # Create processor and add data
        processor = DataProcessor()
        processor.add_data(sample_data)

        # Get statistics
        stats = processor.get_statistics()
        print("Original data statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")

        # Filter outliers
        filtered_data = processor.filter_outliers(threshold=2.0)
        print(f"\nFiltered data: {filtered_data}")

        # Calculate statistics for filtered data
        filtered_stats = calculate_statistics(filtered_data)
        print("\nFiltered data statistics:")
        for key, value in filtered_stats.items():
            print(f"  {key}: {value}")

    except (ValueError, TypeError) as e:
        logging.error(f"Error processing data: {e}")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
