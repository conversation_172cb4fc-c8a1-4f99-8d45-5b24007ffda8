# sample_bad_style.py
# Example of Python code with style issues
# From <PERSON><PERSON>'s Workspace

import os, sys, j<PERSON>
from typing import *


def bad_function(x, y, z=None):
    if x > y:
        return x * 2
    else:
        return y * 2


class badClass:
    def __init__(self, value):
        self.value = value

    def process(self):
        result = self.value**2
        return result


def another_bad_function(a, b):
    # This function has many style issues
    if a == None:
        return None
    elif b == None:
        return None
    else:
        if a > b:
            return a + b
        else:
            return a - b


# Missing blank lines before class definition
class AnotherBadClass:
    def __init__(self, data):
        self.data = data

    def method_with_long_line(
        self, param1, param2, param3, param4, param5, param6, param7, param8
    ):
        return param1 + param2 + param3 + param4 + param5 + param6 + param7 + param8

    def method_with_trailing_whitespace(self):
        x = 1
        y = 2
        return x + y


def function_with_issues():
    # Unused import will be flagged
    import unused_module

    # Bad variable names
    l = [1, 2, 3, 4, 5]
    d = {"a": 1, "b": 2}

    # Bad spacing
    for i in range(len(l)):
        if l[i] > 2:
            d[str(i)] = l[i]

    return d


# Function with no docstring and bad formatting
def calculate(x, y):
    if x < 0 or y < 0:
        return -1
    result = x**2 + y**2
    return result


if __name__ == "__main__":
    # Bad spacing around operators
    x = 5
    y = 10
    result = bad_function(x, y)
    print("Result:", result)

    # Creating instances with bad spacing
    obj = badClass(42)
    processed = obj.process()
    print("Processed:", processed)
