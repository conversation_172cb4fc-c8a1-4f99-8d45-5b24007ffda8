# run_frontend.py
# Simple script to run the Streamlit frontend
# From <PERSON><PERSON>'s Workspace

"""
Simple script to run the Streamlit frontend for development.
"""

import subprocess
import sys
import os


def main():
    """Run the frontend server."""
    print("🎨 Starting AI-Enhanced Code Review Tool Frontend")
    print("=" * 50)
    print("Frontend will be available at: http://localhost:8501")
    print("Make sure the backend is running at: http://localhost:8000")
    print("=" * 50)

    try:
        # Run Streamlit
        subprocess.run(
            [
                sys.executable,
                "-m",
                "streamlit",
                "run",
                "frontend/app.py",
                "--server.port=8501",
                "--server.address=0.0.0.0",
            ]
        )
    except KeyboardInterrupt:
        print("\n👋 Frontend server stopped")
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
