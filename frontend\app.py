# Streamlit frontend for AI-Enhanced Code Review Tool
# From <PERSON><PERSON>'s Workspace

import streamlit as st
import requests
import json
import time
from typing import Dict, Any, List
import io

# Configure Streamlit page
st.set_page_config(
    page_title="AI-Enhanced Code Review Tool",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Backend API configuration
BACKEND_URL = "http://localhost:8000"


def main():
    """Main application function."""
    st.title("🔍 AI-Enhanced Code Review Tool")
    st.markdown(
        "*Analyze Python code for bugs, style issues, and quality metrics - From <PERSON><PERSON>'s Workspace*"
    )

    # Sidebar configuration
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Backend URL configuration
        backend_url = st.text_input(
            "Backend URL", value=BACKEND_URL, help="URL of the backend API service"
        )

        # Analysis options
        st.subheader("Analysis Options")
        show_detailed_feedback = st.checkbox("Show Detailed Feedback", value=True)
        show_features = st.checkbox("Show Extracted Features", value=False)

        # About section
        st.subheader("About")
        st.info(
            "This tool uses machine learning and static analysis to review Python code. "
            "It checks for style issues, predicts potential bugs, and provides comprehensive feedback."
        )

    # Main content area
    tab1, tab2, tab3 = st.tabs(
        ["📝 Code Analysis", "📁 File Upload", "📊 Batch Analysis"]
    )

    with tab1:
        code_analysis_tab(backend_url, show_detailed_feedback, show_features)

    with tab2:
        file_upload_tab(backend_url, show_detailed_feedback, show_features)

    with tab3:
        batch_analysis_tab(backend_url)


def code_analysis_tab(backend_url: str, show_detailed: bool, show_features: bool):
    """Tab for direct code input analysis."""
    st.header("Code Analysis")

    # Code input area
    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("Python Code Input")
        code_input = st.text_area(
            "Enter your Python code here:",
            height=400,
            placeholder="""def example_function(x, y):
    \"\"\"Example function for analysis.\"\"\"
    if x > y:
        return x * 2
    else:
        return y * 2

# Example usage
result = example_function(5, 3)
print(f"Result: {result}")""",
            help="Paste your Python code here for analysis",
        )

    with col2:
        st.subheader("Quick Examples")

        if st.button("📝 Simple Function"):
            st.session_state.example_code = """def calculate_area(length, width):
    return length * width

area = calculate_area(10, 5)
print(f"Area: {area}")"""

        if st.button("🔄 Complex Logic"):
            st.session_state.example_code = """def process_data(data):
    result = []
    for item in data:
        if isinstance(item, (int, float)):
            if item > 0:
                result.append(item ** 2)
            else:
                result.append(0)
        else:
            try:
                num = float(item)
                result.append(num ** 2)
            except ValueError:
                result.append(None)
    return result"""

        if st.button("⚠️ Problematic Code"):
            st.session_state.example_code = """def bad_function(x,y):
    if x>y:
        return x*2
    else:
        for i in range(y):
            x+=i
        return x

result=bad_function(5,3)"""

        # Load example if selected
        if "example_code" in st.session_state:
            code_input = st.session_state.example_code
            del st.session_state.example_code

    # Analysis button and results
    if st.button("🔍 Analyze Code", type="primary", use_container_width=True):
        if code_input.strip():
            analyze_code(code_input, backend_url, show_detailed, show_features)
        else:
            st.error("Please enter some Python code to analyze.")


def file_upload_tab(backend_url: str, show_detailed: bool, show_features: bool):
    """Tab for file upload analysis."""
    st.header("File Upload Analysis")

    uploaded_file = st.file_uploader(
        "Choose a Python file", type=["py"], help="Upload a .py file for analysis"
    )

    if uploaded_file is not None:
        # Display file info
        st.info(f"📁 File: {uploaded_file.name} ({uploaded_file.size} bytes)")

        # Show file content preview
        if st.checkbox("Show file content preview"):
            try:
                content = uploaded_file.read().decode("utf-8")
                st.code(content, language="python")
                uploaded_file.seek(0)  # Reset file pointer
            except UnicodeDecodeError:
                st.error("File must be valid UTF-8 encoded text.")
                return

        # Analyze button
        if st.button("🔍 Analyze File", type="primary"):
            analyze_uploaded_file(
                uploaded_file, backend_url, show_detailed, show_features
            )


def batch_analysis_tab(backend_url: str):
    """Tab for batch analysis of multiple files."""
    st.header("Batch Analysis")
    st.info("Upload multiple Python files for batch analysis")

    uploaded_files = st.file_uploader(
        "Choose Python files",
        type=["py"],
        accept_multiple_files=True,
        help="Upload multiple .py files for batch analysis",
    )

    if uploaded_files:
        st.success(f"📁 {len(uploaded_files)} file(s) uploaded")

        # Display file list
        for i, file in enumerate(uploaded_files):
            st.write(f"{i + 1}. {file.name} ({file.size} bytes)")

        if st.button("🔍 Analyze All Files", type="primary"):
            batch_analyze_files(uploaded_files, backend_url)


def analyze_code(code: str, backend_url: str, show_detailed: bool, show_features: bool):
    """Analyze code using the backend API."""
    with st.spinner("Analyzing code..."):
        try:
            response = requests.post(
                f"{backend_url}/analyze_code/", json={"code_text": code}, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                display_analysis_results(result, show_detailed, show_features)
            else:
                st.error(f"Analysis failed: {response.status_code} - {response.text}")

        except requests.exceptions.ConnectionError:
            st.error(
                "❌ Cannot connect to backend service. Please ensure the backend is running."
            )
        except requests.exceptions.Timeout:
            st.error("⏱️ Analysis timed out. Please try with smaller code.")
        except Exception as e:
            st.error(f"❌ Error during analysis: {str(e)}")


def analyze_uploaded_file(
    uploaded_file, backend_url: str, show_detailed: bool, show_features: bool
):
    """Analyze uploaded file using the backend API."""
    with st.spinner(f"Analyzing {uploaded_file.name}..."):
        try:
            files = {"code_file": uploaded_file}
            response = requests.post(
                f"{backend_url}/analyze_file/", files=files, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                st.success(f"✅ Analysis completed for {uploaded_file.name}")
                display_analysis_results(result, show_detailed, show_features)
            else:
                st.error(f"Analysis failed: {response.status_code} - {response.text}")

        except requests.exceptions.ConnectionError:
            st.error(
                "❌ Cannot connect to backend service. Please ensure the backend is running."
            )
        except requests.exceptions.Timeout:
            st.error("⏱️ Analysis timed out. Please try with smaller file.")
        except Exception as e:
            st.error(f"❌ Error during analysis: {str(e)}")


def batch_analyze_files(uploaded_files: List, backend_url: str):
    """Analyze multiple files in batch."""
    results = []
    progress_bar = st.progress(0)
    status_text = st.empty()

    for i, uploaded_file in enumerate(uploaded_files):
        status_text.text(f"Analyzing {uploaded_file.name}...")

        try:
            files = {"code_file": uploaded_file}
            response = requests.post(
                f"{backend_url}/analyze_file/", files=files, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                result["filename"] = uploaded_file.name
                results.append(result)
            else:
                st.warning(
                    f"Failed to analyze {uploaded_file.name}: {response.status_code}"
                )

        except Exception as e:
            st.warning(f"Error analyzing {uploaded_file.name}: {str(e)}")

        progress_bar.progress((i + 1) / len(uploaded_files))

    status_text.text("Analysis complete!")

    if results:
        display_batch_results(results)


def display_analysis_results(
    result: Dict[str, Any], show_detailed: bool, show_features: bool
):
    """Display analysis results in a formatted way."""

    # Overall score display
    score = result.get("overall_score", 0)
    st.subheader("📊 Analysis Results")

    # Score with color coding
    col1, col2, col3 = st.columns(3)

    with col1:
        score_color = get_score_color(score)
        st.metric(
            "Overall Quality Score",
            f"{score}/100",
            delta=None,
            help="Overall code quality score based on style, bugs, and complexity",
        )
        st.markdown(
            f"<div style='color: {score_color}; font-weight: bold;'>{get_score_grade(score)}</div>",
            unsafe_allow_html=True,
        )

    with col2:
        bug_prediction = result.get("bug_prediction", "Unknown")
        confidence = result.get("confidence", 0)

        prediction_color = "green" if bug_prediction == "Not Buggy" else "red"
        st.metric(
            "Bug Prediction", bug_prediction, delta=f"{confidence:.1%} confidence"
        )
        st.markdown(
            f"<div style='color: {prediction_color};'>ML Analysis</div>",
            unsafe_allow_html=True,
        )

    with col3:
        style_issues = result.get("style_issues", [])
        st.metric("Style Issues", len(style_issues), delta="PEP 8 Compliance")

    # Style issues section
    if style_issues:
        st.subheader("🎨 Style Issues")

        # Group issues by severity
        errors = [
            issue for issue in style_issues if issue["code"].startswith(("E", "F"))
        ]
        warnings = [issue for issue in style_issues if issue["code"].startswith("W")]

        if errors:
            st.error(f"**{len(errors)} Error(s) Found:**")
            for issue in errors[:10]:  # Limit display
                st.write(
                    f"• Line {issue['line']}:{issue['column']} - [{issue['code']}] {issue['message']}"
                )

        if warnings:
            st.warning(f"**{len(warnings)} Warning(s) Found:**")
            for issue in warnings[:10]:  # Limit display
                st.write(
                    f"• Line {issue['line']}:{issue['column']} - [{issue['code']}] {issue['message']}"
                )
    else:
        st.success("✅ No style issues found! Code follows PEP 8 guidelines.")

    # Detailed feedback
    if show_detailed and "feedback" in result:
        display_detailed_feedback(result["feedback"])

    # Features display
    if show_features and "extracted_features" in result:
        display_extracted_features(result["extracted_features"])


def display_detailed_feedback(feedback: Dict[str, List[str]]):
    """Display detailed feedback in expandable sections."""
    st.subheader("📋 Detailed Feedback")

    feedback_sections = [
        ("overall_summary", "📝 Overall Summary", "blue"),
        ("positive_aspects", "✅ Positive Aspects", "green"),
        ("areas_for_improvement", "⚠️ Areas for Improvement", "orange"),
        ("suggestions", "💡 Suggestions", "purple"),
        ("complexity_analysis", "🔄 Complexity Analysis", "gray"),
        ("security_notes", "🔒 Security Notes", "red"),
        ("best_practices", "⭐ Best Practices", "cyan"),
    ]

    for key, title, color in feedback_sections:
        if key in feedback and feedback[key]:
            with st.expander(title, expanded=(key == "overall_summary")):
                for item in feedback[key]:
                    st.write(f"• {item}")


def display_extracted_features(features: Dict[str, int]):
    """Display extracted code features."""
    st.subheader("🔧 Extracted Features")

    with st.expander("Code Structure Features"):
        col1, col2 = st.columns(2)

        with col1:
            st.write("**Functions & Classes:**")
            st.write(f"• Functions: {features.get('FunctionDef_count', 0)}")
            st.write(f"• Async Functions: {features.get('AsyncFunctionDef_count', 0)}")
            st.write(f"• Classes: {features.get('ClassDef_count', 0)}")

            st.write("**Control Flow:**")
            st.write(f"• If statements: {features.get('If_count', 0)}")
            st.write(f"• For loops: {features.get('For_count', 0)}")
            st.write(f"• While loops: {features.get('While_count', 0)}")
            st.write(f"• Try blocks: {features.get('Try_count', 0)}")

        with col2:
            st.write("**Code Metrics:**")
            st.write(f"• Total nodes: {features.get('total_nodes', 0)}")
            st.write(f"• Unique node types: {features.get('unique_node_types', 0)}")
            st.write(f"• Function calls: {features.get('Call_count', 0)}")
            st.write(f"• Assignments: {features.get('Assign_count', 0)}")


def display_batch_results(results: List[Dict[str, Any]]):
    """Display batch analysis results."""
    st.subheader("📊 Batch Analysis Results")

    # Summary statistics
    total_files = len(results)
    avg_score = (
        sum(r.get("overall_score", 0) for r in results) / total_files
        if total_files > 0
        else 0
    )
    buggy_files = sum(1 for r in results if r.get("bug_prediction") == "Buggy")

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Files Analyzed", total_files)
    with col2:
        st.metric("Average Score", f"{avg_score:.1f}/100")
    with col3:
        st.metric("Potential Issues", f"{buggy_files} files")

    # Individual file results
    for result in results:
        with st.expander(
            f"📁 {result['filename']} - Score: {result.get('overall_score', 0)}/100"
        ):
            display_analysis_results(result, show_detailed=False, show_features=False)


def get_score_color(score: float) -> str:
    """Get color based on score."""
    if score >= 90:
        return "green"
    elif score >= 80:
        return "lightgreen"
    elif score >= 70:
        return "orange"
    elif score >= 60:
        return "darkorange"
    else:
        return "red"


def get_score_grade(score: float) -> str:
    """Get letter grade based on score."""
    if score >= 90:
        return "Grade A - Excellent"
    elif score >= 80:
        return "Grade B - Good"
    elif score >= 70:
        return "Grade C - Fair"
    elif score >= 60:
        return "Grade D - Poor"
    else:
        return "Grade F - Critical"


if __name__ == "__main__":
    main()
