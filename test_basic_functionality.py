# test_basic_functionality.py
# Basic functionality test for AI-Enhanced Code Review Tool
# From Hasif's Workspace

"""
Simple test script to verify basic functionality without requiring
all dependencies to be installed.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_feature_extraction():
    """Test feature extraction functionality."""
    print("Testing feature extraction...")
    
    try:
        from backend.feature_extractor import extract_features_from_code
        
        sample_code = '''
def hello_world():
    """Simple test function."""
    print("Hello, World!")
    return True

if __name__ == "__main__":
    hello_world()
'''
        
        features = extract_features_from_code(sample_code)
        
        if features and len(features) > 0:
            print("SUCCESS: Feature extraction working")
            print(f"   Extracted {len(features)} features")
            print(f"   Sample features: FunctionDef_count={features.get('FunctionDef_count', 0)}, total_nodes={features.get('total_nodes', 0)}")
            return True
        else:
            print("FAILED: Feature extraction failed - no features returned")
            return False
            
    except Exception as e:
        print(f"FAILED: Feature extraction failed: {e}")
        return False

def test_style_checking():
    """Test style checking functionality."""
    print("Testing style checking...")
    
    try:
        from backend.style_checker import check_code_style
        
        # Code with style issues
        bad_code = '''
import os,sys
def bad_function(x,y):
    if x>y:
        return x*2
    else:
        return y*2
'''
        
        style_issues = check_code_style(bad_code)
        
        print(f"SUCCESS: Style checking working - found {len(style_issues)} issues")
        if style_issues:
            print(f"   Sample issue: Line {style_issues[0].line} - {style_issues[0].code}")
        return True
        
    except Exception as e:
        print(f"WARNING: Style checking may not work: {e}")
        return True  # Non-critical

def test_ml_prediction():
    """Test ML prediction functionality."""
    print("Testing ML prediction...")
    
    try:
        from backend.ml_predictor import predict_bugs
        
        # Sample features
        sample_features = {
            "FunctionDef_count": 1,
            "If_count": 1,
            "total_nodes": 15,
            "unique_node_types": 8
        }
        
        # Add missing features with default values
        from backend.feature_extractor import MODEL_EXPECTED_FEATURES_ORDER
        for feature_name in MODEL_EXPECTED_FEATURES_ORDER:
            if feature_name not in sample_features:
                sample_features[feature_name] = 0
        
        prediction, confidence = predict_bugs(sample_features)
        
        print(f"SUCCESS: ML prediction working - Result: {prediction} (confidence: {confidence:.2f})")
        return True
        
    except Exception as e:
        print(f"WARNING: ML prediction using fallback: {e}")
        return True  # Non-critical

def test_code_analysis():
    """Test overall code analysis."""
    print("Testing code analysis...")
    
    try:
        from backend.code_analyzer import analyze_code_quality
        from backend.style_checker import StyleIssue
        
        # Mock data
        style_issues = [
            StyleIssue(line=1, column=1, code="E302", message="expected 2 blank lines")
        ]
        
        sample_features = {
            "FunctionDef_count": 1,
            "total_nodes": 15,
            "unique_node_types": 8
        }
        
        score = analyze_code_quality(style_issues, "Not Buggy", 0.8, sample_features)
        
        print(f"SUCCESS: Code analysis working - Score: {score}/100")
        return True
        
    except Exception as e:
        print(f"FAILED: Code analysis failed: {e}")
        return False

def test_feedback_generation():
    """Test feedback generation."""
    print("Testing feedback generation...")
    
    try:
        from backend.feedback_generator import generate_code_feedback
        from backend.style_checker import StyleIssue
        
        # Mock data
        style_issues = [
            StyleIssue(line=1, column=1, code="E302", message="expected 2 blank lines")
        ]
        
        sample_features = {
            "FunctionDef_count": 1,
            "total_nodes": 15,
            "unique_node_types": 8
        }
        
        feedback = generate_code_feedback(style_issues, "Not Buggy", 0.8, sample_features)
        
        if feedback and "overall_summary" in feedback:
            print("SUCCESS: Feedback generation working")
            print(f"   Generated {len(feedback)} feedback categories")
            return True
        else:
            print("FAILED: Feedback generation failed - no feedback returned")
            return False
            
    except Exception as e:
        print(f"FAILED: Feedback generation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("AI-Enhanced Code Review Tool - Basic Functionality Test")
    print("From Hasif's Workspace")
    print("="*60)
    
    tests = [
        test_feature_extraction,
        test_style_checking,
        test_ml_prediction,
        test_code_analysis,
        test_feedback_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"FAILED: Test {test.__name__} crashed: {e}")
        print()
    
    print("="*60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("SUCCESS: All tests passed! Basic functionality is working.")
    elif passed >= total * 0.8:
        print("SUCCESS: Most tests passed! Core functionality is working.")
    else:
        print("WARNING: Some tests failed. Check the output above for details.")
    
    print("\nNext steps:")
    print("   1. Install missing dependencies: conda install -c conda-forge fastapi uvicorn streamlit")
    print("   2. Run the backend: python run_backend.py")
    print("   3. Run the frontend: python run_frontend.py")
    print("   4. Train a model: python train_model.py --create-sample-data")

if __name__ == "__main__":
    main()