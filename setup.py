# setup.py
# Setup script for AI-Enhanced Code Review Tool
# From <PERSON><PERSON>'s Workspace

"""
Setup script for the AI-Enhanced Code Review Tool.
This script helps with initial setup, dependency installation, and basic testing.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description, check=True):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=check, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Requires Python 3.10+")
        return False


def install_dependencies():
    """Install project dependencies with robust error handling."""
    print("📦 Installing dependencies...")
    
    # Upgrade pip first
    print("🔄 Upgrading pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ Pip upgraded successfully")
    except:
        print("⚠️ Could not upgrade pip, continuing...")
    
    # Core dependencies that should work with Python 3.12
    core_dependencies = [
        "numpy>=1.26.0",
        "pandas>=2.1.0", 
        "scikit-learn>=1.3.0",
        "joblib>=1.3.0",
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "pydantic>=2.5.0",
        "requests>=2.31.0",
        "python-multipart>=0.0.6",
        "streamlit>=1.29.0",
        "flake8>=6.0.0"
    ]
    
    print("🔄 Installing core dependencies...")
    success_count = 0
    
    for dep in core_dependencies:
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], check=True, capture_output=True, text=True)
            print(f"✅ {dep} installed successfully")
            success_count += 1
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            # Try without version constraint
            dep_name = dep.split(">=")[0].split("==")[0]
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", dep_name
                ], check=True, capture_output=True, text=True)
                print(f"✅ {dep_name} installed (without version constraint)")
                success_count += 1
            except:
                print(f"❌ Could not install {dep_name} at all")
    
    # Optional development dependencies
    dev_dependencies = ["pytest>=7.4.0", "pytest-cov>=4.1.0", "black>=23.0.0"]
    
    print("🔄 Installing development dependencies...")
    for dep in dev_dependencies:
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], check=True, capture_output=True, text=True)
            print(f"✅ {dep} installed")
        except:
            print(f"⚠️ Could not install {dep} (optional)")
    
    if success_count >= len(core_dependencies) * 0.8:  # 80% success rate
        print(f"✅ Successfully installed {success_count}/{len(core_dependencies)} core dependencies")
        return True
    else:
        print(f"❌ Only installed {success_count}/{len(core_dependencies)} core dependencies")
        return False


def create_sample_model():
    """Create a sample ML model for testing."""
    print("🤖 Creating sample ML model...")
    
    try:
        # Create sample training data and train a model
        result = subprocess.run([
            sys.executable, "train_model.py", "--create-sample-data", "--sample-size", "500"
        ], check=True, capture_output=True, text=True)
        
        # Check if model was created
        model_path = Path("models/bug_detector_model.joblib")
        if model_path.exists():
            print("✅ Sample model created successfully")
            return True
        else:
            print("❌ Model file not found after training")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Error creating sample model: {e}")
        print("⚠️ This is optional - the system can work without a trained model")
        return False
    except Exception as e:
        print(f"❌ Unexpected error creating sample model: {e}")
        return False


def run_tests():
    """Run the test suite."""
    print("🧪 Running tests...")
    
    # Check if pytest is available
    try:
        import pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ], check=False, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("⚠️ Some tests failed, but continuing...")
            return True
    except ImportError:
        print("⚠️ pytest not installed, skipping tests")
        return True
    except Exception as e:
        print(f"⚠️ Error running tests: {e}")
        return True


def verify_setup():
    """Verify that the setup is working correctly."""
    print("🔍 Verifying setup...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Test basic imports
        print("🔄 Testing imports...")
        
        # Test feature extraction
        from backend.feature_extractor import extract_features_from_code
        sample_code = '''
def test_function():
    """Test function."""
    return "Hello, World!"
'''
        features = extract_features_from_code(sample_code)
        if features:
            print("✅ Feature extraction working")
        else:
            print("❌ Feature extraction failed")
            return False
        
        # Test style checking (if flake8 is available)
        try:
            from backend.style_checker import check_code_style
            style_issues = check_code_style(sample_code)
            print("✅ Style checking working")
        except Exception as e:
            print(f"⚠️ Style checking may not work: {e}")
        
        # Test ML predictor
        try:
            from backend.ml_predictor import predict_bugs
            prediction, confidence = predict_bugs(features)
            print("✅ ML prediction working")
        except Exception as e:
            print(f"⚠️ ML prediction may not work: {e}")
        
        print("✅ Basic verification completed")
        return True
        
    except Exception as e:
        print(f"❌ Setup verification failed: {e}")
        return False


def print_usage_instructions():
    """Print usage instructions."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\n📋 Quick Start Guide:")
    print("\n1. Start the backend server:")
    print("   python run_backend.py")
    print("   (Available at: http://localhost:8000)")
    
    print("\n2. In a new terminal, start the frontend:")
    print("   python run_frontend.py")
    print("   (Available at: http://localhost:8501)")
    
    print("\n3. Or use Docker Compose:")
    print("   docker-compose up --build")
    
    print("\n4. Train a custom model:")
    print("   python train_model.py --create-sample-data")
    
    print("\n5. Run tests:")
    print("   python -m pytest tests/")
    
    print("\n📁 Project Structure:")
    print("   backend/     - FastAPI backend with ML models")
    print("   frontend/    - Streamlit web interface")
    print("   tests/       - Unit tests")
    print("   examples/    - Sample Python files for testing")
    print("   models/      - Trained ML models")
    print("   data/        - Training data and datasets")
    
    print("\n📚 Documentation:")
    print("   API Docs: http://localhost:8000/docs")
    print("   README.md for detailed information")
    
    print("\n🔧 Troubleshooting:")
    print("   - If dependencies fail to install, try: pip install --upgrade pip")
    print("   - For style checking issues, ensure flake8 is installed")
    print("   - For ML model issues, run: python train_model.py --create-sample-data")
    
    print("\n" + "="*60)


def main():
    """Main setup function."""
    print("🚀 AI-Enhanced Code Review Tool Setup")
    print("From Hasif's Workspace")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Critical dependencies failed to install")
        print("⚠️ You may need to install dependencies manually")
        print("Try: pip install numpy pandas scikit-learn fastapi streamlit")
    
    # Create sample model (optional)
    create_sample_model()
    
    # Run tests (optional)
    run_tests()
    
    # Verify setup
    if not verify_setup():
        print("⚠️ Setup verification had issues, but basic functionality may still work")
    
    # Print usage instructions
    print_usage_instructions()


if __name__ == "__main__":
    main()
