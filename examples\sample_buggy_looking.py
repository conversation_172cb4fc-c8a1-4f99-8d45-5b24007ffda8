# sample_buggy_looking.py
# Example of code with potential bug patterns
# From <PERSON><PERSON>'s Workspace

import os
import sys


def divide_numbers(a, b):
    """Function with potential division by zero."""
    return a / b  # No check for b == 0


def access_list_element(lst, index):
    """Function with potential index error."""
    return lst[index]  # No bounds checking


def process_file(filename):
    """Function with potential file handling issues."""
    file = open(filename, "r")  # No error handling, file not closed
    content = file.read()
    return content


def recursive_function(n):
    """Function with potential infinite recursion."""
    if n > 0:
        return n * recursive_function(n - 1)
    # Missing base case for n <= 0


def string_operations(text):
    """Function with potential string issues."""
    if text:  # Should check for None explicitly
        return text.upper().strip()
    return text.lower()  # This will fail if text is None


def dictionary_access(data, key):
    """Function with potential KeyError."""
    return data[key]  # No check if key exists


def type_confusion(value):
    """Function with potential type errors."""
    if value:
        return value + 10  # Assumes value is numeric
    return 0


def loop_with_modification(items):
    """Function with potential loop modification issues."""
    for item in items:
        if item < 0:
            items.remove(item)  # Modifying list during iteration
    return items


def variable_scope_issue():
    """Function with variable scope problems."""
    for i in range(5):
        x = i * 2
    return x  # x might not be defined if range is empty


def exception_handling_issue():
    """Function with poor exception handling."""
    try:
        result = 10 / 0
        return result
    except:  # Bare except clause
        pass  # Silent failure


class ProblematicClass:
    """Class with potential issues."""

    def __init__(self, data):
        self.data = data
        self.processed = []

    def process_data(self):
        """Method with potential issues."""
        for item in self.data:
            try:
                # Potential type issues
                result = item * 2 + "suffix"
                self.processed.append(result)
            except:
                continue  # Silent failure

    def get_item(self, index):
        """Method with potential index error."""
        return self.processed[index]  # No bounds checking

    def update_data(self, new_data):
        """Method that might cause issues."""
        self.data = new_data
        # Doesn't clear processed list, causing inconsistency


def complex_buggy_function(data):
    """Complex function with multiple potential issues."""
    results = []

    # Potential None reference
    if data:
        for i in range(len(data)):
            try:
                # Multiple potential issues
                item = data[i]
                processed = item / (i - 2)  # Division by zero when i=2

                # Type assumption
                if processed > item:
                    results.append(processed.upper())  # Assumes string
                else:
                    results.append(processed[0])  # Assumes indexable

            except Exception as e:
                # Poor error handling
                results.append(None)
                continue

    # Potential empty list access
    return results[0]


def memory_leak_potential():
    """Function that might cause memory issues."""
    large_list = []

    # Potential infinite loop
    i = 0
    while i < 1000000:
        large_list.append([0] * 1000)
        if i % 100000 == 0:
            i += 1  # Only increments occasionally

    return large_list


def main():
    """Main function with various problematic calls."""
    # These calls demonstrate the potential issues

    try:
        # Division by zero
        result1 = divide_numbers(10, 0)

        # Index error
        result2 = access_list_element([1, 2, 3], 10)

        # File error
        result3 = process_file("nonexistent_file.txt")

        # Dictionary error
        data = {"a": 1, "b": 2}
        result4 = dictionary_access(data, "c")

        # Type error
        result5 = type_confusion("hello")

        print("All operations completed successfully")

    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
