# train_model.py
# Script for training bug detection models
# From Hasif's Workspace

"""
This script provides functionality to train machine learning models
for bug detection based on extracted code features.
"""

import argparse
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import joblib
import logging
import os
from typing import Tuple, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def load_training_data(data_path: str) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Load training data from CSV file.

    Args:
        data_path: Path to the CSV file containing features and labels.

    Returns:
        Tuple of (features DataFrame, labels Series).
    """
    logger.info(f"Loading training data from: {data_path}")

    if not os.path.exists(data_path):
        raise FileNotFoundError(f"Training data file not found: {data_path}")

    # Load data
    data = pd.read_csv(data_path)
    logger.info(f"Loaded {len(data)} samples with {len(data.columns)} columns")

    # Separate features and labels
    # Assume the last column is the label (is_buggy)
    if "is_buggy" in data.columns:
        label_column = "is_buggy"
    elif "label" in data.columns:
        label_column = "label"
    else:
        # Use the last column as label
        label_column = data.columns[-1]
        logger.warning(
            f"No 'is_buggy' or 'label' column found. Using '{label_column}' as label."
        )

    features = data.drop(columns=[label_column])
    labels = data[label_column]

    logger.info(f"Features shape: {features.shape}")
    logger.info(f"Labels distribution: {labels.value_counts().to_dict()}")

    return features, labels


def train_model(
    features: pd.DataFrame, labels: pd.Series, model_type: str = "random_forest"
) -> Any:
    """
    Train a machine learning model.

    Args:
        features: Feature DataFrame.
        labels: Labels Series.
        model_type: Type of model to train ('random_forest' or 'logistic_regression').

    Returns:
        Trained model.
    """
    logger.info(f"Training {model_type} model...")

    if model_type == "random_forest":
        model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1,
        )
    elif model_type == "logistic_regression":
        model = LogisticRegression(random_state=42, max_iter=1000, solver="liblinear")
    else:
        raise ValueError(f"Unsupported model type: {model_type}")

    # Train the model
    model.fit(features, labels)
    logger.info("Model training completed")

    return model


def evaluate_model(model: Any, X_test: pd.DataFrame, y_test: pd.Series) -> dict:
    """
    Evaluate the trained model.

    Args:
        model: Trained model.
        X_test: Test features.
        y_test: Test labels.

    Returns:
        Dictionary containing evaluation metrics.
    """
    logger.info("Evaluating model performance...")

    # Make predictions
    y_pred = model.predict(X_test)
    y_pred_proba = (
        model.predict_proba(X_test)[:, 1] if hasattr(model, "predict_proba") else None
    )

    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)

    # Print detailed results
    print("\n" + "=" * 50)
    print("MODEL EVALUATION RESULTS")
    print("=" * 50)
    print(f"Accuracy: {accuracy:.4f}")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))
    print("\nConfusion Matrix:")
    print(confusion_matrix(y_test, y_pred))

    # Feature importance (if available)
    if hasattr(model, "feature_importances_"):
        feature_importance = pd.DataFrame(
            {"feature": X_test.columns, "importance": model.feature_importances_}
        ).sort_values("importance", ascending=False)

        print("\nTop 10 Most Important Features:")
        print(feature_importance.head(10).to_string(index=False))

    return {"accuracy": accuracy, "predictions": y_pred, "probabilities": y_pred_proba}


def save_model(model: Any, output_path: str) -> None:
    """
    Save the trained model to disk.

    Args:
        model: Trained model to save.
        output_path: Path where to save the model.
    """
    logger.info(f"Saving model to: {output_path}")

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Save model
    joblib.dump(model, output_path)
    logger.info("Model saved successfully")


def create_sample_training_data(output_path: str, num_samples: int = 1000) -> None:
    """
    Create sample training data for demonstration purposes.

    Args:
        output_path: Path where to save the sample data.
        num_samples: Number of samples to generate.
    """
    logger.info(f"Creating sample training data with {num_samples} samples...")

    # Import feature order from backend
    try:
        from backend.feature_extractor import MODEL_EXPECTED_FEATURES_ORDER

        feature_names = MODEL_EXPECTED_FEATURES_ORDER
    except ImportError:
        # Fallback feature names
        feature_names = [
            "FunctionDef_count",
            "ClassDef_count",
            "If_count",
            "For_count",
            "Try_count",
            "Call_count",
            "total_nodes",
            "unique_node_types",
        ]

    # Generate random features
    np.random.seed(42)
    data = {}

    for feature in feature_names:
        if "count" in feature:
            # Generate count features (0-20)
            data[feature] = np.random.poisson(3, num_samples)
        elif feature == "total_nodes":
            # Generate total nodes (10-200)
            data[feature] = np.random.randint(10, 201, num_samples)
        elif feature == "unique_node_types":
            # Generate unique node types (5-30)
            data[feature] = np.random.randint(5, 31, num_samples)
        else:
            # Default random integers
            data[feature] = np.random.randint(0, 10, num_samples)

    # Create labels based on some heuristics
    df = pd.DataFrame(data)

    # Simple heuristic: high complexity = more likely to be buggy
    complexity_score = (
        df["If_count"] * 0.3
        + df["For_count"] * 0.2
        + df["Try_count"] * 0.1
        + (df["total_nodes"] / 50) * 0.4
    )

    # Add some randomness
    random_factor = np.random.normal(0, 1, num_samples)
    final_score = complexity_score + random_factor

    # Convert to binary labels (top 30% are "buggy")
    threshold = np.percentile(final_score, 70)
    df["is_buggy"] = (final_score > threshold).astype(int)

    # Save to CSV
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    df.to_csv(output_path, index=False)
    logger.info(f"Sample training data saved to: {output_path}")

    # Print summary
    print(f"\nSample data created:")
    print(f"  Total samples: {len(df)}")
    print(f"  Features: {len(feature_names)}")
    print(f"  Buggy samples: {df['is_buggy'].sum()} ({df['is_buggy'].mean():.1%})")


def main():
    """Main function for training models."""
    parser = argparse.ArgumentParser(description="Train bug detection models")
    parser.add_argument("--data", type=str, help="Path to training data CSV file")
    parser.add_argument(
        "--output",
        type=str,
        default="models/bug_detector_model.joblib",
        help="Output path for trained model",
    )
    parser.add_argument(
        "--model-type",
        type=str,
        default="random_forest",
        choices=["random_forest", "logistic_regression"],
        help="Type of model to train",
    )
    parser.add_argument(
        "--test-size",
        type=float,
        default=0.2,
        help="Fraction of data to use for testing",
    )
    parser.add_argument(
        "--create-sample-data", action="store_true", help="Create sample training data"
    )
    parser.add_argument(
        "--sample-size",
        type=int,
        default=1000,
        help="Number of samples in generated data",
    )

    args = parser.parse_args()

    # Create sample data if requested
    if args.create_sample_data:
        sample_data_path = "data/sample_training_data.csv"
        create_sample_training_data(sample_data_path, args.sample_size)
        if not args.data:
            args.data = sample_data_path

    if not args.data:
        print(
            "Error: Please provide training data with --data or use --create-sample-data"
        )
        return

    try:
        # Load data
        features, labels = load_training_data(args.data)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=args.test_size, random_state=42, stratify=labels
        )

        logger.info(f"Training set: {len(X_train)} samples")
        logger.info(f"Test set: {len(X_test)} samples")

        # Train model
        model = train_model(X_train, y_train, args.model_type)

        # Evaluate model
        evaluation_results = evaluate_model(model, X_test, y_test)

        # Save model
        save_model(model, args.output)

        print(f"\nTraining completed successfully!")
        print(f"Model saved to: {args.output}")
        print(f"Test accuracy: {evaluation_results['accuracy']:.4f}")

    except Exception as e:
        logger.error(f"Error during training: {e}")
        raise


if __name__ == "__main__":
    main()
