# Root requirements for AI-Enhanced Code Review Tool
# From <PERSON><PERSON>'s Workspace

# Core dependencies (Python 3.12 compatible versions)
pandas>=2.1.0
scikit-learn>=1.3.0
joblib>=1.3.0
flake8>=6.0.0
numpy>=1.26.0

# Web frameworks
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
streamlit>=1.29.0

# Data validation and HTTP
pydantic>=2.5.0
requests>=2.31.0
python-multipart>=0.0.6

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0

# Optional dependencies for extended functionality
# matplotlib>=3.8.0
# seaborn>=0.13.0
# PyGithub>=1.59.0